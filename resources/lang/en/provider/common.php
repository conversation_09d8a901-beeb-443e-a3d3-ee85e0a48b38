<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Provider Common Language Lines (English)
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for common provider dashboard elements.
    | You are free to modify these language lines according to your application's
    | requirements.
    |
    */

    // Common actions
    'actions' => [
        'view' => 'View',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'create' => 'Create',
        'update' => 'Update',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'confirm' => 'Confirm',
        'back' => 'Back',
        'next' => 'Next',
        'previous' => 'Previous',
        'submit' => 'Submit',
        'reset' => 'Reset',
        'search' => 'Search',
        'filter' => 'Filter',
        'export' => 'Export',
        'import' => 'Import',
        'download' => 'Download',
        'upload' => 'Upload',
        'refresh' => 'Refresh',
    ],

    // Status labels
    'status' => [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'pending' => 'Pending',
        'confirmed' => 'Confirmed',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
        'draft' => 'Draft',
        'published' => 'Published',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
    ],

    // Table elements
    'table' => [
        'actions' => 'Actions',
        'no_data' => 'No Data Available',
        'no_data_description' => 'There are no records to display at this time.',
        'loading' => 'Loading...',
        'select_all' => 'Select All',
    ],

    // Pagination
    'pagination' => [
        'showing' => 'Showing',
        'to' => 'to',
        'of' => 'of',
        'results' => 'results',
        'navigation' => 'Pagination Navigation',
        'previous' => 'Previous',
        'next' => 'Next',
        'first' => 'First',
        'last' => 'Last',
        'go_to_page' => 'Go to page :page',
        'current_page' => 'Current page :page',
    ],

    // Search and filters
    'search' => [
        'placeholder' => 'Search...',
        'search' => 'Search',
        'reset' => 'Reset',
        'sort_by' => 'Sort By',
        'direction' => 'Direction',
        'ascending' => 'Ascending',
        'descending' => 'Descending',
        'filters' => 'Filters',
        'clear_filters' => 'Clear Filters',
    ],

    // Confirmations
    'confirmations' => [
        'delete' => 'Are you sure you want to delete this item?',
        'delete_multiple' => 'Are you sure you want to delete the selected items?',
        'action' => 'Are you sure you want to perform this action?',
        'unsaved_changes' => 'You have unsaved changes. Are you sure you want to leave?',
    ],

    // Form elements
    'form' => [
        'required' => 'Required',
        'optional' => 'Optional',
        'choose_file' => 'Choose File',
        'no_file_chosen' => 'No file chosen',
        'select_option' => 'Select an option',
        'enter_value' => 'Enter value',
        'required_fields_missing' => 'Please fill in all required fields.',
    ],

    // Time and dates
    'time' => [
        'just_now' => 'Just now',
        'minutes_ago' => ':count minutes ago',
        'hours_ago' => ':count hours ago',
        'days_ago' => ':count days ago',
        'weeks_ago' => ':count weeks ago',
        'months_ago' => ':count months ago',
        'years_ago' => ':count years ago',
    ],

    // File sizes
    'file_size' => [
        'bytes' => 'bytes',
        'kb' => 'KB',
        'mb' => 'MB',
        'gb' => 'GB',
    ],
];
