<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Provider Authentication Language Lines (English)
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for provider authentication.
    | You are free to modify these language lines according to your application's
    | requirements.
    |
    */

    // Page titles
    'title' => 'Provider Dashboard - Authentication',
    'description' => 'Provider Dashboard Authentication',
    'login_title' => 'Provider Dashboard - Sign In',
    'login_description' => 'Sign in to provider dashboard',
    'signup_title' => 'Provider Dashboard - Sign Up',
    'signup_description' => 'Create your provider account',

    // Login form
    'sign_in' => 'Sign In',
    'welcome_back' => 'Welcome back to your dashboard',
    'email' => 'Email',
    'email_placeholder' => '<EMAIL>',
    'password' => 'Password',
    'password_placeholder' => 'Enter Password',
    'remember_me' => 'Remember me',
    'sign_in_button' => 'Sign In',
    'logout' => 'Log out',
    'dont_have_account' => "Don't have an account?",
    'sign_up_link' => 'Sign Up',

    // Additional links
    'need_help' => 'Need help?',
    'contact_support' => 'Contact Support',

    // Validation messages
    'email_required' => 'Email is required',
    'email_invalid' => 'Please enter a valid email address',
    'password_required' => 'Password is required',
    'invalid_credentials' => 'Invalid credentials',
    'account_inactive' => 'Your account is inactive. Please contact support.',
    'account_pending_approval' => 'Your account is pending approval. Please wait for admin approval.',

    // Success messages
    'login_successful' => 'Welcome back! You have been successfully logged in.',
    'logout_successful' => 'You have been successfully logged out.',

    // Error messages
    'login_failed' => 'Invalid email or password. Please try again.',
    'logout_error' => 'An error occurred during logout.',

    // Registration form
    'sign_up' => 'Sign Up',
    'create_account_description' => 'Create your provider account to get started',
    'name_arabic' => 'Business Name (Arabic)',
    'name_english' => 'Business Name (English)',
    'name_arabic_placeholder' => 'Enter business name in Arabic',
    'name_english_placeholder' => 'Enter business name in English',
    'phone' => 'Phone Number',
    'phone_placeholder' => '05xxxxxxxx',
    'optional' => 'Optional',
    'password_requirements' => 'Minimum 8 characters with uppercase, lowercase, number and symbol',
    'password_confirmation' => 'Confirm Password',
    'password_confirmation_placeholder' => 'Confirm your password',
    'accept_policy_text' => 'I agree to the',
    'terms_and_conditions' => 'Terms and Conditions',
    'and' => 'and',
    'privacy_policy' => 'Privacy Policy',
    'create_account_button' => 'Create Account',
    'already_have_account' => 'Already have an account?',
    'sign_in_link' => 'Sign In',

    // Registration validation messages
    'name_arabic_required' => 'Business name in Arabic is required',
    'name_english_required' => 'Business name in English is required',
    'name_arabic_min' => 'Business name in Arabic must be at least 2 characters',
    'name_english_min' => 'Business name in English must be at least 2 characters',
    'name_arabic_max' => 'Business name in Arabic may not be greater than 255 characters',
    'name_english_max' => 'Business name in English may not be greater than 255 characters',
    'email_exists' => 'This email address is already registered',
    'password_min' => 'Password must be at least 8 characters',
    'password_confirmation_mismatch' => 'Password confirmation does not match',
    'password_confirmation_required' => 'Password confirmation is required',
    'policy_acceptance_required' => 'You must accept the terms and conditions',
    'phone_invalid_saudi' => 'Please enter a valid Saudi phone number (05xxxxxxxx)',

    // Registration success/error messages
    'registration_successful' => 'Account created successfully! Please check your email to verify your account.',
    'registration_failed' => 'Registration failed. Please try again.',
    'verification_invalid' => 'Invalid verification link.',
    'email_already_verified' => 'Email address is already verified.',
    'email_verified_successfully' => 'Email verified successfully! You can now sign in.',
    'verification_failed' => 'Email verification failed.',
    'provider_not_found' => 'Provider account not found.',
    'verification_resent' => 'Verification email has been resent.',

    // Field labels for validation
    'description_arabic' => 'Description (Arabic)',
    'description_english' => 'Description (English)',
];
