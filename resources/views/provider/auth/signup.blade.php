@extends('provider.layouts.auth')

@section('title', __('provider/auth.signup_title'))
@section('description', __('provider/auth.signup_description'))

@section('content')
    <form action="{{ route('provider.register') }}" class="card-body flex flex-col gap-5 p-10" id="sign_up_form"
        method="post">
        @csrf

        <!-- Flash Messages -->
        @include('provider.components.flash-messages')

        <!-- Header Section -->
        <div class="text-center mb-2.5">
            <h3 class="text-lg font-medium text-gray-900 leading-none mb-2.5">
                {{ __('provider/auth.sign_up') }}
            </h3>
            <p class="text-2sm text-gray-700">
                {{ __('provider/auth.create_account_description') }}
            </p>
        </div>

        <!-- Arabic Name Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('provider/auth.name_arabic') }} <span class="text-danger">*</span>
            </label>
            <input class="input @error('translations.ar.name') border-danger @enderror" name="translations[ar][name]"
                placeholder="{{ __('provider/auth.name_arabic_placeholder') }}" type="text" dir="rtl"
                value="{{ old('translations.ar.name') }}" autocomplete="organization" />
            @error('translations.ar.name')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- English Name Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('provider/auth.name_english') }} <span class="text-danger">*</span>
            </label>
            <input class="input @error('translations.en.name') border-danger @enderror" name="translations[en][name]"
                placeholder="{{ __('provider/auth.name_english_placeholder') }}" type="text" dir="ltr"
                value="{{ old('translations.en.name') }}" autocomplete="organization" />
            @error('translations.en.name')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- Email Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('provider/auth.email') }} <span class="text-danger">*</span>
            </label>
            <input class="input @error('email') border-danger @enderror" name="email"
                placeholder="{{ __('provider/auth.email_placeholder') }}" type="email" dir="ltr" value="{{ old('email') }}"
                autocomplete="email" />
            @error('email')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- Phone Field (Optional) -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('provider/auth.phone') }} <span
                    class="text-2xs text-gray-600">({{ __('provider/auth.optional') }})</span>
            </label>
            <input class="input @error('phone') border-danger @enderror" name="phone"
                placeholder="{{ __('provider/auth.phone_placeholder') }}" type="tel" value="{{ old('phone') }}"
                autocomplete="tel" />
            @error('phone')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- Password Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('provider/auth.password') }} <span class="text-danger">*</span>
            </label>
            <div class="input @error('password') border-danger @enderror" data-toggle-password="true">
                <input name="password" placeholder="{{ __('provider/auth.password_placeholder') }}" type="password"
                    autocomplete="new-password" />
                <button class="btn btn-icon" data-toggle-password-trigger="true" type="button">
                    <i class="ki-filled ki-eye text-gray-500 toggle-password-active:hidden"></i>
                    <i class="ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block"></i>
                </button>
            </div>
            @error('password')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
            <span class="form-hint text-gray-600">
                {{ __('provider/auth.password_requirements') }}
            </span>
        </div>

        <!-- Confirm Password Field -->
        <div class="flex flex-col gap-1">
            <label class="form-label font-normal text-gray-900">
                {{ __('provider/auth.password_confirmation') }} <span class="text-danger">*</span>
            </label>
            <div class="input @error('password_confirmation') border-danger @enderror" data-toggle-password="true">
                <input name="password_confirmation"
                    placeholder="{{ __('provider/auth.password_confirmation_placeholder') }}" type="password"
                    autocomplete="new-password" />
                <button class="btn btn-icon" data-toggle-password-trigger="true" type="button">
                    <i class="ki-filled ki-eye text-gray-500 toggle-password-active:hidden"></i>
                    <i class="ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block"></i>
                </button>
            </div>
            @error('password_confirmation')
                <span class="form-hint text-danger">{{ $message }}</span>
            @enderror
        </div>

        <!-- Policy Acceptance Checkbox -->
        <label class="checkbox-group">
            <input class="checkbox checkbox-sm @error('accept_policy') border-danger @enderror" name="accept_policy"
                type="checkbox" value="1" {{ old('accept_policy') ? 'checked' : '' }} />
            <span class="checkbox-label">
                {{ __('provider/auth.accept_policy_text') }}
                <a href="#" class="text-primary hover:text-primary-active">
                    {{ __('provider/auth.terms_and_conditions') }}
                </a>
                {{ __('provider/auth.and') }}
                <a href="#" class="text-primary hover:text-primary-active">
                    {{ __('provider/auth.privacy_policy') }}
                </a>
                <span class="text-danger">*</span>
            </span>
        </label>
        @error('accept_policy')
            <span class="form-hint text-danger">{{ $message }}</span>
        @enderror

        <!-- Submit Button -->
        <button class="btn btn-primary flex justify-center grow" type="submit">
            {{ __('provider/auth.create_account_button') }}
        </button>

        <!-- Login Link -->
        <div class="text-center">
            <span class="text-2sm text-gray-700">
                {{ __('provider/auth.already_have_account') }}
            </span>
            <a href="{{ route('provider.login') }}" class="text-2sm text-primary font-medium hover:text-primary-active">
                {{ __('provider/auth.sign_in_link') }}
            </a>
        </div>

        <!-- Language Toggle Switcher -->
        @php
            use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
            $currentLocale = LaravelLocalization::getCurrentLocale();
            $supportedLocales = LaravelLocalization::getSupportedLocales();

            // Determine the alternative language
            $alternativeLocale = $currentLocale === 'ar' ? 'en' : 'ar';
            $alternativeLanguage = $supportedLocales[$alternativeLocale] ?? null;
            $alternativeUrl = LaravelLocalization::getLocalizedURL($alternativeLocale);
        @endphp

        @if($alternativeLanguage)
            <div class="flex items-center gap-2 mt-6">
                <span class="border-t border-gray-200 w-full"></span>
            </div>

            <div class="text-center mt-4">
                <div class="flex items-center justify-center">
                    <span class="text-2sm text-gray-700 me-1.5 {{ $currentLocale === 'ar' ? 'ml-2' : 'mr-2' }}">
                        {{ $currentLocale === 'ar' ? 'تغيير اللغة؟' : 'Change language?' }}
                    </span>

                    <a href="{{ $alternativeUrl }}" class="btn btn-link">
                        {{ $alternativeLanguage['native'] }}
                    </a>
                </div>
            </div>
        @endif
    </form>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Focus on first name field when page loads
            const firstNameInput = document.querySelector('input[name="translations[ar][name]"]');
            if (firstNameInput) {
                firstNameInput.focus();
            }

            // Password strength indicator (optional enhancement)
            const passwordInput = document.querySelector('input[name="password"]');
            const confirmPasswordInput = document.querySelector('input[name="password_confirmation"]');

            if (passwordInput && confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', function () {
                    if (this.value && passwordInput.value !== this.value) {
                        this.setCustomValidity('{{ __("provider/auth.password_confirmation_mismatch") }}');
                    } else {
                        this.setCustomValidity('');
                    }
                });
            }
        });
    </script>
@endpush