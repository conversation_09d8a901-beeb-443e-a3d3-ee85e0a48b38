<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_providers', function (Blueprint $table) {
            // Add authentication fields
            $table->string('email')->unique()->after('id');
            $table->timestamp('email_verified_at')->nullable()->after('email');
            $table->string('password')->after('email_verified_at');
            $table->rememberToken()->after('password');

            // Add indexes for authentication
            $table->index('email');
            $table->index('email_verified_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_providers', function (Blueprint $table) {
            // Remove authentication fields
            $table->dropIndex(['email']);
            $table->dropIndex(['email_verified_at']);
            $table->dropColumn([
                'email',
                'email_verified_at',
                'password',
                'remember_token'
            ]);
        });
    }
};
