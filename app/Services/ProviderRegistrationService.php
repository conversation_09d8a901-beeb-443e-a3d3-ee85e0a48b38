<?php

namespace App\Services;

use App\Models\ServiceProvider;
use App\Repositories\Contracts\ServiceProviderRepositoryInterface;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Event;

/**
 * Provider Registration Service
 * 
 * Handles business logic for provider registration including
 * account creation, email verification setup, and initial configuration.
 */
class ProviderRegistrationService
{
    public function __construct(
        private ServiceProviderRepositoryInterface $serviceProviderRepository
    ) {
    }

    /**
     * Register a new service provider.
     *
     * @param array $data Registration data including translations
     * @return ServiceProvider
     * @throws \Exception
     */
    public function register(array $data): ServiceProvider
    {
        return DB::transaction(function () use ($data) {
            // Extract translations
            $translations = $data['translations'] ?? [];
            unset($data['translations']);

            // Prepare provider data
            $providerData = [
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'phone' => $data['phone'] ?? null,
                'is_active' => false, // Inactive until email verification
                'rating' => 0.00,
            ];

            // Create the provider
            $provider = $this->serviceProviderRepository->create($providerData);

            // Add translations
            foreach ($translations as $locale => $translation) {
                $provider->translateOrNew($locale)->name = $translation['name'];
                if (!empty($translation['description'])) {
                    $provider->translateOrNew($locale)->description = $translation['description'];
                }
            }

            $provider->save();

            // Fire the registered event for email verification
            Event::dispatch(new Registered($provider));

            return $provider;
        });
    }

    /**
     * Verify provider email and activate account.
     *
     * @param ServiceProvider $provider
     * @return bool
     */
    public function verifyEmail(ServiceProvider $provider): bool
    {
        if ($provider->hasVerifiedEmail()) {
            return true;
        }

        $verified = $provider->markEmailAsVerified();

        if ($verified) {
            // Activate the provider account after email verification
            $this->serviceProviderRepository->update($provider->id, [
                'is_active' => true
            ]);
        }

        return $verified;
    }

    /**
     * Resend email verification notification.
     *
     * @param ServiceProvider $provider
     * @return void
     */
    public function resendEmailVerification(ServiceProvider $provider): void
    {
        if (!$provider->hasVerifiedEmail()) {
            $provider->sendEmailVerificationNotification();
        }
    }

    /**
     * Check if email is available for registration.
     *
     * @param string $email
     * @return bool
     */
    public function isEmailAvailable(string $email): bool
    {
        return !$this->serviceProviderRepository->existsByEmail($email);
    }

    /**
     * Get provider by email for verification purposes.
     *
     * @param string $email
     * @return ServiceProvider|null
     */
    public function getProviderByEmail(string $email): ?ServiceProvider
    {
        return $this->serviceProviderRepository->findByEmail($email);
    }

    /**
     * Update provider profile after registration.
     *
     * @param ServiceProvider $provider
     * @param array $data
     * @return ServiceProvider
     */
    public function updateProfile(ServiceProvider $provider, array $data): ServiceProvider
    {
        // Extract translations
        $translations = $data['translations'] ?? [];
        unset($data['translations']);

        // Update basic provider data
        $provider = $this->serviceProviderRepository->update($provider->id, $data);

        // Update translations if provided
        if (!empty($translations)) {
            foreach ($translations as $locale => $translation) {
                if (!empty($translation['name'])) {
                    $provider->translateOrNew($locale)->name = $translation['name'];
                }
                if (isset($translation['description'])) {
                    $provider->translateOrNew($locale)->description = $translation['description'];
                }
            }
            $provider->save();
        }

        return $provider;
    }

    /**
     * Deactivate provider account.
     *
     * @param ServiceProvider $provider
     * @return bool
     */
    public function deactivateAccount(ServiceProvider $provider): bool
    {
        return $this->serviceProviderRepository->update($provider->id, [
            'is_active' => false
        ]) !== null;
    }

    /**
     * Activate provider account.
     *
     * @param ServiceProvider $provider
     * @return bool
     */
    public function activateAccount(ServiceProvider $provider): bool
    {
        return $this->serviceProviderRepository->update($provider->id, [
            'is_active' => true
        ]) !== null;
    }
}
