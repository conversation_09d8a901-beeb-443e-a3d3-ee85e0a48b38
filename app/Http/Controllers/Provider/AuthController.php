<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;
use App\Services\LanguageService;
use App\Services\ProviderRegistrationService;
use App\Http\Requests\Provider\ProviderRegistrationRequest;
use Illuminate\Auth\Events\Verified;

/**
 * Provider Authentication Controller
 * 
 * Handles authentication for service provider dashboard.
 * Manages login, logout, and authentication-related functionality.
 */
class AuthController extends Controller
{
    public function __construct(
        private LanguageService $languageService,
        private ProviderRegistrationService $registrationService
    ) {
    }

    /**
     * Show the provider login form.
     */
    public function showLoginForm(): View|RedirectResponse
    {
        // Redirect to dashboard if already authenticated
        if (Auth::guard('provider')->check()) {
            return redirect()->route('provider.dashboard');
        }

        return view('provider.auth.login', [
            'languageService' => $this->languageService
        ]);
    }

    /**
     * Handle provider login request.
     */
    public function login(Request $request): RedirectResponse
    {
        // Validate the login request
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput($request->only('email'));
        }

        // Attempt to authenticate the provider
        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if (Auth::guard('provider')->attempt($credentials, $remember)) {
            // Get the authenticated provider
            $provider = Auth::guard('provider')->user();

            // Check if provider is active
            if (!$provider->is_active) {
                Auth::guard('provider')->logout();
                return back()
                    ->withInput($request->only('email'))
                    ->with('error', __('provider/auth.account_inactive'));
            }

            // Check if provider is approved (if you have approval system)
            if (isset($provider->status) && $provider->status !== 'approved') {
                Auth::guard('provider')->logout();
                return back()
                    ->withInput($request->only('email'))
                    ->with('error', __('provider/auth.account_pending_approval'));
            }

            // Regenerate session to prevent session fixation
            $request->session()->regenerate();

            // Redirect to intended page or dashboard
            $intended = $request->session()->pull('url.intended');
            if ($intended) {
                return redirect($intended)->with('success', __('provider/auth.login_successful'));
            }

            return redirect()->route('provider.dashboard')
                ->with('success', __('provider/auth.login_successful'));
        }

        // Authentication failed
        return back()
            ->withInput($request->only('email'))
            ->with('error', __('provider/auth.login_failed'));
    }

    /**
     * Handle provider logout request.
     */
    public function logout(Request $request): RedirectResponse
    {
        // Log out the provider
        Auth::guard('provider')->logout();

        // Invalidate the session
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        // Get the current locale for redirect
        $locale = LaravelLocalization::getCurrentLocale();
        $loginUrl = LaravelLocalization::getLocalizedURL($locale, '/login');

        return redirect($loginUrl)
            ->with('success', __('provider/auth.logout_successful'));
    }

    /**
     * Show the provider password reset form.
     */
    public function showResetForm(): View
    {
        return view('provider.auth.reset-password');
    }

    /**
     * Handle provider password reset request.
     */
    public function sendResetLink(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:service_providers,email',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput($request->only('email'));
        }

        // Here you would implement password reset logic
        // For now, just return success message
        return back()
            ->with('success', __('provider/auth.reset_link_sent'));
    }

    /**
     * Show the provider registration form.
     */
    public function showRegistrationForm(): View|RedirectResponse
    {
        // Redirect to dashboard if already authenticated
        if (Auth::guard('provider')->check()) {
            return redirect()->route('provider.dashboard');
        }

        return view('provider.auth.signup', [
            'languageService' => $this->languageService
        ]);
    }

    /**
     * Handle provider registration request.
     */
    public function register(ProviderRegistrationRequest $request): RedirectResponse
    {
        try {
            // Register the provider
            $provider = $this->registrationService->register($request->validated());

            // Send email verification notification
            $provider->sendEmailVerificationNotification();

            return redirect()->route('provider.login')
                ->with('success', __('provider/auth.registration_successful'));

        } catch (\Exception $e) {
            return back()
                ->withInput($request->except('password', 'password_confirmation'))
                ->with('error', __('provider/auth.registration_failed'));
        }
    }

    /**
     * Handle email verification.
     */
    public function verifyEmail(Request $request): RedirectResponse
    {
        $provider = $this->registrationService->getProviderByEmail($request->route('email'));

        if (!$provider) {
            return redirect()->route('provider.login')
                ->with('error', __('provider/auth.verification_invalid'));
        }

        if ($provider->hasVerifiedEmail()) {
            return redirect()->route('provider.login')
                ->with('info', __('provider/auth.email_already_verified'));
        }

        if ($this->registrationService->verifyEmail($provider)) {
            event(new Verified($provider));

            return redirect()->route('provider.login')
                ->with('success', __('provider/auth.email_verified_successfully'));
        }

        return redirect()->route('provider.login')
            ->with('error', __('provider/auth.verification_failed'));
    }

    /**
     * Resend email verification notification.
     */
    public function resendVerification(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:service_providers,email',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput($request->only('email'));
        }

        $provider = $this->registrationService->getProviderByEmail($request->email);

        if (!$provider) {
            return back()
                ->with('error', __('provider/auth.provider_not_found'));
        }

        if ($provider->hasVerifiedEmail()) {
            return back()
                ->with('info', __('provider/auth.email_already_verified'));
        }

        $this->registrationService->resendEmailVerification($provider);

        return back()
            ->with('success', __('provider/auth.verification_resent'));
    }
}
