<?php

namespace App\Http\Requests\Provider;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

/**
 * Provider Registration Request
 *
 * Handles validation for provider registration form including
 * name, email, password, and policy acceptance validation.
 */
class ProviderRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Translatable name fields
            'translations.ar.name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],
            'translations.en.name' => [
                'required',
                'string',
                'max:255',
                'min:2'
            ],

            // Optional description fields
            'translations.ar.description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'translations.en.description' => [
                'nullable',
                'string',
                'max:1000'
            ],

            // Authentication fields
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'unique:service_providers,email'
            ],

            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
            ],

            'password_confirmation' => [
                'required',
                'same:password'
            ],

            // Policy acceptance
            'accept_policy' => [
                'required',
                'accepted'
            ],

            // Optional phone number with Saudi validation
            'phone' => [
                'nullable',
                'string',
                'regex:/^(05|5|\+9665)[0-9]{8}$/'
            ],
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'translations.ar.name.required' => __('provider/auth.name_arabic_required'),
            'translations.en.name.required' => __('provider/auth.name_english_required'),
            'translations.ar.name.min' => __('provider/auth.name_arabic_min'),
            'translations.en.name.min' => __('provider/auth.name_english_min'),
            'translations.ar.name.max' => __('provider/auth.name_arabic_max'),
            'translations.en.name.max' => __('provider/auth.name_english_max'),

            'email.required' => __('provider/auth.email_required'),
            'email.email' => __('provider/auth.email_invalid'),
            'email.unique' => __('provider/auth.email_exists'),

            'password.required' => __('provider/auth.password_required'),
            'password.min' => __('provider/auth.password_min'),
            'password.confirmed' => __('provider/auth.password_confirmation_mismatch'),
            'password_confirmation.required' => __('provider/auth.password_confirmation_required'),
            'password_confirmation.same' => __('provider/auth.password_confirmation_mismatch'),

            'accept_policy.required' => __('provider/auth.policy_acceptance_required'),
            'accept_policy.accepted' => __('provider/auth.policy_acceptance_required'),

            'phone.regex' => __('provider/auth.phone_invalid_saudi'),
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'translations.ar.name' => __('provider/auth.name_arabic'),
            'translations.en.name' => __('provider/auth.name_english'),
            'translations.ar.description' => __('provider/auth.description_arabic'),
            'translations.en.description' => __('provider/auth.description_english'),
            'email' => __('provider/auth.email'),
            'password' => __('provider/auth.password'),
            'password_confirmation' => __('provider/auth.password_confirmation'),
            'accept_policy' => __('provider/auth.policy_acceptance'),
            'phone' => __('provider/auth.phone'),
        ];
    }
}
