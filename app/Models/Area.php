<?php

namespace App\Models;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Area extends Model implements TranslatableContract
{
    use HasFactory, SoftDeletes, Translatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'city_id',
        // Non-translatable fields
    ];

    /**
     * The attributes that are translatable.
     *
     * @var array<int, string>
     */
    public $translatedAttributes = [
        'name',
    ];

    /**
     * Get the city that owns the area.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the customers for the area.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the service providers for the area.
     */
    public function serviceProviders(): HasMany
    {
        return $this->hasMany(ServiceProvider::class);
    }

    /**
     * Scope a query to filter by city.
     */
    public function scopeInCity($query, int $cityId)
    {
        return $query->where('city_id', $cityId);
    }

    /**
     * Scope a query to search by name.
     */
    public function scopeSearchByName($query, string $name)
    {
        return $query->where('name', 'like', '%' . $name . '%');
    }

    /**
     * Scope a query to get areas with city.
     */
    public function scopeWithCity($query)
    {
        return $query->with('city');
    }

    /**
     * Scope a query to get areas with customers count.
     */
    public function scopeWithCustomersCount($query)
    {
        return $query->withCount('customers');
    }

    /**
     * Scope a query to get areas with service providers count.
     */
    public function scopeWithServiceProvidersCount($query)
    {
        return $query->withCount('serviceProviders');
    }
}
