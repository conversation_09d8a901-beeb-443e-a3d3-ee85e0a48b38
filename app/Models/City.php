<?php

namespace App\Models;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class City extends Model implements TranslatableContract
{
    use HasFactory, SoftDeletes, Translatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        // Non-translatable fields can be added here if needed
    ];

    /**
     * The attributes that are translatable.
     *
     * @var array<int, string>
     */
    public $translatedAttributes = [
        'name',
    ];

    /**
     * Get the areas for the city.
     */
    public function areas(): HasMany
    {
        return $this->hasMany(Area::class);
    }

    /**
     * Get the customers for the city.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the service providers for the city.
     */
    public function serviceProviders(): HasMany
    {
        return $this->hasMany(ServiceProvider::class);
    }

    /**
     * Scope a query to search by name.
     */
    public function scopeSearchByName($query, string $name)
    {
        return $query->where('name', 'like', '%' . $name . '%');
    }

    /**
     * Scope a query to get cities with areas.
     */
    public function scopeWithAreas($query)
    {
        return $query->with('areas');
    }

    /**
     * Scope a query to get cities with customers count.
     */
    public function scopeWithCustomersCount($query)
    {
        return $query->withCount('customers');
    }

    /**
     * Scope a query to get cities with service providers count.
     */
    public function scopeWithServiceProvidersCount($query)
    {
        return $query->withCount('serviceProviders');
    }
}
