<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

/**
 * Service Provider Model
 * 
 * Represents service providers in the system with authentication capabilities,
 * multilingual support, and location-based features.
 */
class ServiceProvider extends Authenticatable implements MustVerifyEmail, TranslatableContract
{
    use HasFactory, SoftDeletes, Notifiable, Translatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'password',
        'logo',
        'rating',
        'city_id',
        'area_id',
        'longitude',
        'latitude',
        'is_active',
        'email_verified_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'rating' => 'decimal:2',
        'longitude' => 'decimal:8',
        'latitude' => 'decimal:8',
        'is_active' => 'boolean',
    ];

    /**
     * Translatable attributes.
     *
     * @var array
     */
    public $translatedAttributes = ['name', 'description'];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'service_providers';

    /**
     * Get the city that the provider belongs to.
     */
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get the area that the provider belongs to.
     */
    public function area()
    {
        return $this->belongsTo(Area::class);
    }

    /**
     * Get the services offered by this provider.
     */
    public function services()
    {
        return $this->hasMany(ProviderService::class);
    }

    /**
     * Get the active services offered by this provider.
     */
    public function activeServices()
    {
        return $this->hasMany(ProviderService::class)->where('is_active', true);
    }

    /**
     * Get the gallery images for this provider.
     */
    public function gallery()
    {
        return $this->hasMany(ServiceProviderGallery::class);
    }

    /**
     * Get the working hours for this provider.
     */
    public function workingHours()
    {
        return $this->hasMany(WorkingHour::class);
    }

    /**
     * Scope a query to only include active providers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include verified providers.
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * Get the full location string.
     */
    public function getFullLocationAttribute(): ?string
    {
        if ($this->city && $this->area) {
            return $this->area->name . ', ' . $this->city->name;
        }

        if ($this->city) {
            return $this->city->name;
        }

        return null;
    }

    /**
     * Get the average price of services offered by this provider.
     */
    public function getAveragePriceAttribute(): ?float
    {
        if ($this->relationLoaded('services') && $this->services->isNotEmpty()) {
            return round($this->services->avg('price'), 2);
        }

        return null;
    }

    /**
     * Check if the provider has verified their email.
     */
    public function hasVerifiedEmail(): bool
    {
        return !is_null($this->email_verified_at);
    }

    /**
     * Mark the given provider's email as verified.
     */
    public function markEmailAsVerified(): bool
    {
        return $this->forceFill([
            'email_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Send the email verification notification.
     */
    public function sendEmailVerificationNotification(): void
    {
        // This will be implemented when we create the notification
        // $this->notify(new ProviderEmailVerificationNotification);
    }

    /**
     * Get the email address that should be used for verification.
     */
    public function getEmailForVerification(): string
    {
        return $this->email;
    }
}
